/* eslint-disable */
import { create } from 'zustand'

const usePrivateChatStore = create((set, get) => ({

  privateChatPinnedMessages: {},

  // Actions
  setPrivateChatPinnedMessages: (pinnedMessages) => {
    set({ privateChatPinnedMessages: pinnedMessages })
  },

  // Pin a message for a specific participant
  pinMessageForParticipant: (participantId, message) => {
    set((state) => ({
      privateChatPinnedMessages: {
        ...state.privateChatPinnedMessages,
        [participantId]: message
      }
    }))
  },

  // Unpin a message for a specific participant
  unpinMessageForParticipant: (participantId) => {
    set((state) => {
      const newPinnedMessages = { ...state.privateChatPinnedMessages }
      delete newPinnedMessages[participantId]
      return { privateChatPinnedMessages: newPinnedMessages }
    })
  },

  // Toggle pin - if same message is pinned, unpin it
  togglePinMessageForParticipant: (participantId, message) => {
    set((state) => {
      const currentPinned = state.privateChatPinnedMessages[participantId]
      
      if (currentPinned?.id === message?.id) {
        // Same message is pinned, so unpin it
        const newPinnedMessages = { ...state.privateChatPinnedMessages }
        delete newPinnedMessages[participantId]
        return { privateChatPinnedMessages: newPinnedMessages }
      } else {
        // Pin the new message
        return {
          privateChatPinnedMessages: {
            ...state.privateChatPinnedMessages,
            [participantId]: message
          }
        }
      }
    })
  },

  // Clear all pinned messages
  clearAllPinnedMessages: () => {
    set({ privateChatPinnedMessages: {} })
  }
}))

export default usePrivateChatStore





