/* eslint-disable */
import { create } from 'zustand'

const usePublicChatStore = create((set, get) => ({

  publicChatPinnedMessages: null,

  setPublicChatPinnedMessages: (message) => {
    set({ publicChatPinnedMessages: message })
  },

  clearPublicChatPinnedMessages: () => {
    set({ publicChatPinnedMessages: null })
  },

  // togglePublicChatPinnedMessage: (message) => {
  //   const currentPinned = get().publicChatPinnedMessages
  //   if (currentPinned?.id === message?.id) {
  //     set({ publicChatPinnedMessages: null })
  //   } else {
  //     set({ publicChatPinnedMessages: message })
  //   }
  // }
}))

export default usePublicChatStore





